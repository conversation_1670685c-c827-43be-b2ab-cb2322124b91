<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络数据包监控</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .status {
            font-size: 14px;
            opacity: 0.9;
        }

        .table-container {
            height: 500px;
            overflow-y: auto;
            position: relative;
            scroll-behavior: smooth;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-header {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table th {
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            font-size: 12px;
            white-space: nowrap;
        }

        .table-body {
            position: relative;
        }

        .packet-row {
            transition: background-color 0.3s ease;
            opacity: 0;
            transform: translateY(-20px);
        }

        .packet-row:nth-child(even) {
            background-color: #f8f9fa;
        }

        .packet-row:hover {
            background-color: #e3f2fd;
        }

        .packet-row.new-packet {
            background: linear-gradient(90deg, #4caf50, transparent);
            animation: highlight 2s ease-out;
        }

        @keyframes highlight {
            0% { background: linear-gradient(90deg, #4caf50, transparent); }
            100% { background: transparent; }
        }

        .table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 150px;
        }

        .direction-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
        }

        .direction-out {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .direction-in {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .type-badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 8px;
            font-size: 10px;
            background: #e9ecef;
            color: #495057;
            border: 1px solid #ced4da;
        }

        .client-id {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            color: #6c757d;
        }

        .timestamp {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            color: #6c757d;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            padding: 15px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 4px;
        }

        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络数据包监控</h1>
            <div class="status">实时监控中... 每3秒自动更新</div>
        </div>
        
        <div class="table-container">
            <table class="table">
                <thead class="table-header">
                    <tr>
                        <th>Packet ID</th>
                        <th>Timestamp</th>
                        <th>ClientID</th>
                        <th>Direction</th>
                        <th>Type</th>
                        <th>Topic</th>
                        <th>Payload (Preview)</th>
                        <th>Length</th>
                        <th>QoS</th>
                        <th>Retain</th>
                    </tr>
                </thead>
                <tbody class="table-body" id="packetTableBody">
                </tbody>
            </table>
            
            <div class="loading-indicator" id="loadingIndicator">
                <div class="spinner"></div>
            </div>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalPackets">0</div>
                <div class="stat-label">总数据包</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="inPackets">0</div>
                <div class="stat-label">入站数据包</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="outPackets">0</div>
                <div class="stat-label">出站数据包</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="avgLength">0</div>
                <div class="stat-label">平均长度</div>
            </div>
        </div>
    </div>

    <script>
        class PacketMonitor {
            constructor() {
                this.packets = [];
                this.packetCounter = 1;
                this.tableBody = document.getElementById('packetTableBody');
                this.totalPacketsEl = document.getElementById('totalPackets');
                this.inPacketsEl = document.getElementById('inPackets');
                this.outPacketsEl = document.getElementById('outPackets');
                this.avgLengthEl = document.getElementById('avgLength');
                
                this.clientIds = ['test', 'mqttable_a9a83899', 'device_001', 'sensor_hub', 'gateway_01'];
                this.topics = ['temperature', 'humidity', 'status', 'config', 'heartbeat', 'data/stream'];
                this.types = ['PINGREQ', 'PINGRESP', 'PUBLISH', 'SUBSCRIBE', 'CONNECT'];
                
                this.stats = {
                    total: 0,
                    inbound: 0,
                    outbound: 0,
                    totalLength: 0
                };

                this.init();
            }

            init() {
                // 初始化一些数据
                this.generateInitialData();
                
                // 开始定时添加新数据
                this.startAutoGeneration();
                
                // 更新统计信息
                this.updateStats();
            }

            generateInitialData() {
                for (let i = 0; i < 8; i++) {
                    setTimeout(() => {
                        this.addNewPacket(false);
                    }, i * 200);
                }
            }

            startAutoGeneration() {
                setInterval(() => {
                    this.addNewPacket(true);
                }, 3000);
            }

            generateRandomPacket() {
                const now = new Date();
                const timestamp = `${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}.${String(now.getMilliseconds()).padStart(3, '0')}`;
                
                const directions = ['IN', 'OUT'];
                const direction = directions[Math.floor(Math.random() * directions.length)];
                const clientId = this.clientIds[Math.floor(Math.random() * this.clientIds.length)];
                const type = this.types[Math.floor(Math.random() * this.types.length)];
                const topic = this.topics[Math.floor(Math.random() * this.topics.length)];
                const length = Math.floor(Math.random() * 1024);
                const qos = Math.floor(Math.random() * 3);
                const retain = Math.random() > 0.7 ? 'Yes' : 'No';

                return {
                    id: `PKT_${String(this.packetCounter++).padStart(4, '0')}`,
                    timestamp,
                    clientId,
                    direction,
                    type,
                    topic,
                    payload: `{"data": "sample_${Math.floor(Math.random() * 1000)}"}`,
                    length,
                    qos,
                    retain
                };
            }

            addNewPacket(animate = true) {
                const packet = this.generateRandomPacket();
                this.packets.unshift(packet);

                // 创建新行
                const row = this.createPacketRow(packet, animate);
                
                if (animate) {
                    // 添加新数据高亮效果
                    row.classList.add('new-packet');
                    setTimeout(() => {
                        row.classList.remove('new-packet');
                    }, 2000);
                }

                // 插入到表格顶部
                this.tableBody.insertBefore(row, this.tableBody.firstChild);

                // 动画效果
                if (animate) {
                    gsap.fromTo(row, 
                        {
                            opacity: 0,
                            y: -30,
                            scale: 0.95
                        },
                        {
                            opacity: 1,
                            y: 0,
                            scale: 1,
                            duration: 0.6,
                            ease: "back.out(1.7)"
                        }
                    );

                    // 向下推动其他行
                    const otherRows = Array.from(this.tableBody.children).slice(1);
                    gsap.to(otherRows, {
                        y: 5,
                        duration: 0.3,
                        stagger: 0.02,
                        yoyo: true,
                        repeat: 1,
                        ease: "power2.out"
                    });

                    // 自动滚动到顶部显示最新数据
                    setTimeout(() => {
                        const container = this.tableBody.parentElement;
                        gsap.to(container, {
                            scrollTop: 0,
                            duration: 0.8,
                            ease: "power2.out"
                        });
                    }, 100);
                } else {
                    gsap.set(row, { opacity: 1, y: 0 });
                    gsap.fromTo(row,
                        { opacity: 0 },
                        { opacity: 1, duration: 0.5, delay: Math.random() * 0.3 }
                    );
                    
                    // 初始化数据也需要滚动到顶部
                    setTimeout(() => {
                        const container = this.tableBody.parentElement;
                        container.scrollTop = 0;
                    }, 100);
                }

                // 更新统计信息
                this.updatePacketStats(packet);
                this.updateStats();
            }

            createPacketRow(packet, animate = true) {
                const row = document.createElement('tr');
                row.className = 'packet-row';
                
                row.innerHTML = `
                    <td>${packet.id}</td>
                    <td class="timestamp">${packet.timestamp}</td>
                    <td class="client-id">${packet.clientId}</td>
                    <td><span class="direction-badge direction-${packet.direction.toLowerCase()}">${packet.direction}</span></td>
                    <td><span class="type-badge">${packet.type}</span></td>
                    <td>${packet.topic}</td>
                    <td style="max-width: 200px;" title="${packet.payload}">${packet.payload}</td>
                    <td>${packet.length}</td>
                    <td>${packet.qos}</td>
                    <td>${packet.retain}</td>
                `;

                // 添加悬停效果
                row.addEventListener('mouseenter', () => {
                    gsap.to(row, {
                        scale: 1.02,
                        duration: 0.2,
                        ease: "power2.out"
                    });
                });

                row.addEventListener('mouseleave', () => {
                    gsap.to(row, {
                        scale: 1,
                        duration: 0.2,
                        ease: "power2.out"
                    });
                });

                return row;
            }

            updatePacketStats(packet) {
                this.stats.total++;
                this.stats.totalLength += packet.length;
                
                if (packet.direction === 'IN') {
                    this.stats.inbound++;
                } else {
                    this.stats.outbound++;
                }
            }

            updateStats() {
                const avgLength = this.stats.total > 0 ? Math.round(this.stats.totalLength / this.stats.total) : 0;

                // 动画更新统计数字
                gsap.to({ val: parseInt(this.totalPacketsEl.textContent) || 0 }, {
                    val: this.stats.total,
                    duration: 0.5,
                    ease: "power2.out",
                    onUpdate: function() {
                        document.getElementById('totalPackets').textContent = Math.round(this.targets()[0].val);
                    }
                });

                gsap.to({ val: parseInt(this.inPacketsEl.textContent) || 0 }, {
                    val: this.stats.inbound,
                    duration: 0.5,
                    ease: "power2.out",
                    onUpdate: function() {
                        document.getElementById('inPackets').textContent = Math.round(this.targets()[0].val);
                    }
                });

                gsap.to({ val: parseInt(this.outPacketsEl.textContent) || 0 }, {
                    val: this.stats.outbound,
                    duration: 0.5,
                    ease: "power2.out",
                    onUpdate: function() {
                        document.getElementById('outPackets').textContent = Math.round(this.targets()[0].val);
                    }
                });

                gsap.to({ val: parseInt(this.avgLengthEl.textContent) || 0 }, {
                    val: avgLength,
                    duration: 0.5,
                    ease: "power2.out",
                    onUpdate: function() {
                        document.getElementById('avgLength').textContent = Math.round(this.targets()[0].val);
                    }
                });
            }
        }

        // 启动监控
        document.addEventListener('DOMContentLoaded', () => {
            new PacketMonitor();
        });
    </script>
</body>
</html>